/* eslint-disable @typescript-eslint/no-unused-vars */
import { Database } from 'better-sqlite3';
import log from '../utils/logs';
import DatabaseManager from '../db/sqlite';
import { Group, GroupRequest } from '../interfaces/Campaigns';

export default class CampaignGroupServices {

  private db: Database;

  public static STATUS_NEW: 'new';

  public static STATUS_DONE: 'done';

  public static STATUS_FALSE: 'false';

  public static STATUS_PENDING: 'pending';

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  getAllByCampaignId(campaignId: string|number): Group[] {
    try {
      return this.db
      .prepare(`SELECT * FROM groups WHERE campaign_id = ?`)
      .all(campaignId) as Group[];
    } catch (error) {
      return [];
    }
  }

  createGroupList(data: GroupRequest): boolean {
    if (data.groupIds.length === 0) {
      log.error("Create groups failed because data is empty");
      return false;
    }

    try {
      const insertGroup = this.db.prepare(`
        INSERT INTO groups (campaign_id, groupID, status)
        VALUES (?, ?, ?)
      `);

      const insertManyGroups = this.db.transaction((items: GroupRequest) => {
        items.groupIds.forEach((groupId) => {
          insertGroup.run(items.campaignId, groupId, 'new');
        });
      });

      insertManyGroups(data);
      return true;
    } catch (error) {
      log.error("An error occurred when create groups", error);
      return false;
    }
  }

  deleteGroupByCampaignId(campaignId: string| number): boolean {
    try {
      this.db.prepare(`DELETE FROM groups WHERE campaign_id = ?`).run(campaignId);
      return true;
    } catch (error) {
      log.error(`An error occurred when delete groups by campaignId ${campaignId}`, error);
      return false;
    }
  }

  updateGroupList(data: GroupRequest): boolean {

    const records = this.getAllByCampaignId(data.campaignId);
    if (records && records.length > 0) {
      const isDeleted = this.deleteGroupByCampaignId(data.campaignId);
      if (!isDeleted) {
        return false;
      }
    }

    return this.createGroupList(data);
  }
}
