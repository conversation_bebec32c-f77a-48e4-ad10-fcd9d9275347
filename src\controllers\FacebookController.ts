import { Page } from 'puppeteer-core';
import AuthServices from '../services/facebook/AuthServices';
import GroupPostService from '../services/facebook/GroupPostService';
import GroupSearchServices from '../services/facebook/GroupSearchService';
import AccountFBServices from '../services/AccountFBServices';
import CampaignsServices from '../services/CampaignsServices'
import { FBContentPostRequest, FBSearchGroupsResponse } from '../types/FacebookGroup';
import { FBUserLoginResponse } from '../types/FacebookUser';
import { IFBUser } from '../interfaces/IFacebookUser';
import { CampaignDetailsForRun, CampaignProgress, Group, User, UserGroup } from '../interfaces/Campaigns';
import log from '../utils/logs';
import { delay } from '../utils/helper';

export default class FacebookController {
  private authServices: AuthServices;

  private groupSearchServices: GroupSearchServices;

  private groupPostServices: GroupPostService;

  private accountFBServices: AccountFBServices;

  private campaignsServices: CampaignsServices;

  private stoppedCampaigns: Record<string, boolean> = {};

  private stoppall: boolean = false;

  constructor() {
    this.authServices = new AuthServices();
    this.groupPostServices = new GroupPostService();
    this.groupSearchServices = new GroupSearchServices();
    this.accountFBServices = new AccountFBServices();
    this.campaignsServices = new CampaignsServices();
  }

  /**
   * Handle login Facebook
   */
  async login(data: {username: string, password: string, profileId: string}): Promise<FBUserLoginResponse> {
    const result = await this.authServices.login(data);
    return result;
  }

  /**
   * Get logged page
   * @param profileId
   */
  protected async getLoggedPage(profileId: string) {
    const { success, page, user, error } = await this.authServices.getLoggedPage(profileId);
    if (success === false || !page || !user) {
      return {
        success: false,
        error,
      };
    }

    return { success: true, page, user }
  }

  /**
   * Post to group
   * @param profileId
   * @param data
   */
  async postGroup(page: Page, user: any, groupId: string, content: FBContentPostRequest, isJoinGroup: boolean) {
    const response = await this.groupPostServices.postToGroup(page, groupId, user, content, isJoinGroup);
    return response;
  }

  /**
   * Search groups
   * @param text
   * @returns
   */
  async searchGroups(
    profileId: string,
    text: string,
    targetGroup: number,
  ): Promise<FBSearchGroupsResponse> {
    const result = await this.getLoggedPage(profileId);
    if (result.success === false || !result.page) {
      return result;
    }

    const { page } = result;
    const response = this.groupSearchServices.searchGroups(page, text, targetGroup);
    return response;
  }

  private async getAccountsForRun(campaign: CampaignDetailsForRun, ListUser: IFBUser[]): Promise<{ finalUserList: IFBUser[]; profilePostCount: Record<string, number>; }> {
    
    const campaignId = campaign.Campaign.id;
    let finalUserList: IFBUser[] = [];
    const profilePostCount: Record<string, number> = {};

      if (campaign.User && campaign.User.length > 0) {
        const existingProfileIds = new Set(campaign.User.map(u => u.profileId));
        const newUsers = ListUser.filter(user => !existingProfileIds.has(user.profileId));

        const accountForRuns = newUsers.map((user) => {
          const id = this.campaignsServices.create_listUser(campaignId, user.profileId);
          if (!id) {
            log.error(`Lỗi thêm tài khoản profileId ${user.profileId} cho chiến dịch ${campaignId}`);
          }
        });

        if (accountForRuns.length === 0) {
        log.error(`Không có tài khoản nào để chạy chiến dịch ${campaignId}`);
        return { finalUserList: [], profilePostCount: {} };
        }

        const allProfileIds = [...existingProfileIds, ...newUsers.map(u => u.profileId)];
        const fetchedUsers = await this.accountFBServices.getUserbyprofileId(allProfileIds);

        if (fetchedUsers === null || fetchedUsers.length === 0) {
          this.stoppedCampaigns[campaignId] = true;
          log.error(`Không tìm thấy tài khoản profile ${allProfileIds} để chạy chiến dịch ${campaignId}`);
          return { finalUserList: [], profilePostCount: {} };
        }

      finalUserList = fetchedUsers;

      const userPostCountMap = new Map<string, number>();
      campaign.User.forEach(user => userPostCountMap.set(user.profileId, user.numbersend ?? 0));
      finalUserList.forEach(user => {
        profilePostCount[user.profileId] = userPostCountMap.get(user.profileId) ?? 0;
      });

    } else {
      finalUserList = ListUser;
      const accountForRuns = ListUser.map((user) => {
        profilePostCount[user.profileId] = 0;
        const id = this.campaignsServices.create_listUser(campaignId, user.profileId);
        if (!id) {
          log.error(`Lỗi thêm tài khoản profileId ${user.profileId} cho chiến dịch ${campaignId}`);
        }
      });
      if (accountForRuns.length === 0) {
        log.error(`Không có tài khoản nào để chạy chiến dịch ${campaignId}`);
        return { finalUserList: [], profilePostCount: {} };
      }
    }

    return { finalUserList, profilePostCount };
  }

  /**
   * Search groups
   * @param list_username
   * @param id
   */
  async start(ListUser: IFBUser[], campaignId: string, sendProgress: ({message, campaignId, action}: CampaignProgress) => void) {
    try {
    
      const campaign = this.campaignsServices.getDataForRunCampaign(campaignId);
      
      if (!campaign) {
        sendProgress({ message: `Không tìm thấy cấu hình hoặc data cho chiến dịch`, campaignId, action: 'stopped' });
        return { success: false, message: `Không tìm thấy cấu hình hoặc data cho chiến dịch ${campaignId}` };
      }
      
      this.stoppedCampaigns[campaignId] = false;
      sendProgress({ message: 'Chiến dịch chuẩn bị khởi chạy', campaignId });
      
      const isStatusUpdated = this.campaignsServices.updateCampaignStatus(campaignId, 'running');
      if (!isStatusUpdated) {
        log.error(`Lỗi cập nhật trạng thái Running cho chiến dịch ${campaignId}`);
      }

      const { delay: campaignDelay, max_post , is_joingroup } = campaign.CampaignConfiguration;
      const { groupIds, imagePaths } = campaign;

      const baseContent: FBContentPostRequest = {
        message: campaign.Campaign?.message,
        filePaths: imagePaths.length > 0 ? imagePaths : [],
      };

      const { finalUserList, profilePostCount } = await this.getAccountsForRun(campaign, ListUser);
      if (finalUserList.length === 0) {
        const updatedCampaign = this.campaignsServices.updateCampaignStatus(campaignId, 'stopped');
        if (!updatedCampaign) {
          log.error(`Lỗi cập nhật trạng thái Stopped cho chiến dịch ${campaignId}`);
        }
        sendProgress({ message: `Không có tài khoản nào để chạy chiến dịch`, campaignId, action: 'stopped' });
        return { success: false, message: `Không có tài khoản nào để chạy chiến dịch ${campaignId}` };
      }

      const profileSessions: Record<string, { page: Page; user: IFBUser, name: any }> = {};

      await Promise.all(
        finalUserList.map(async (User) => {
          const isUpdated = this.accountFBServices.updateUser({ ...User, status : 'running'});
          if (!isUpdated) {
            log.error(`Lỗi cập nhật trạng thái cho tài khoản ${User.username}`);
          }
          const result = await this.getLoggedPage(User.profileId);
          if (result.success && result.page) {
            profileSessions[User.profileId] = { page: result.page, user: User, name: result.user };
          } else {
            log.warn(`Lỗi khi truy cập profile page của tài khoản ${User.username}`);
          }
        })
      );

      const profileIds = Object.keys(profileSessions);

      const groupSuccessStatus: Record<string, boolean> = {};
      const groupTriedBy: Record<string, Set<string>> = {};
      const groupLocked: Set<string> = new Set();

      groupIds.forEach(gid => {
        groupSuccessStatus[gid] = false;
        groupTriedBy[gid] = new Set();
        const usergroup = this.campaignsServices.getUserGroup(campaignId, gid);
        usergroup?.forEach(user => {
          groupTriedBy[gid].add(user.profileId);
        });
      });

      while (true) {
        if (this.stoppedCampaigns[campaignId]){
          this.stoppedCampaigns[campaignId] = false;
          
          await Promise.all(Object.values(profileSessions).map(s => s.page.close()));
          const updatedCampaign = this.campaignsServices.updateCampaignStatus(campaignId, 'stopped');
          if (!updatedCampaign) {
            log.error(`Lỗi cập nhật trạng thái Stopped cho chiến dịch ${campaignId}`);
          }
          
          sendProgress({ message: 'Chiến dịch đã dừng', campaignId, action: 'stopped' });
          return { success: true, message: 'Chiến dịch đã dừng' };
        }

        const availableProfiles = profileIds.filter(pid => profilePostCount[pid] < max_post);
        const remainingGroups = groupIds.filter(
          gid => !groupSuccessStatus[gid] && availableProfiles.some(pid => !groupTriedBy[gid].has(pid))
        );

        if (availableProfiles.length === 0 || remainingGroups.length === 0) break;

        const tasks: Promise<void>[] = [];

        for (const profileId of availableProfiles) {
          const session = profileSessions[profileId];
          if (!session) continue;

          // Tìm nhóm mà tài khoản này chưa thử và nhóm chưa thành công
          const groupId = groupIds.find(
            gid => !groupSuccessStatus[gid] && !groupTriedBy[gid].has(profileId) && !groupLocked.has(gid)
          );
          sendProgress({ message: `Nhóm đang đợi xử lý ${groupId}`, campaignId });

          if (!groupId) continue;

          groupTriedBy[groupId].add(profileId); // đánh dấu đã thử
          groupLocked.add(groupId);

          const task = (async () => {
            try {
              const response = await this.postGroup(session.page, session.name, groupId, baseContent, is_joingroup);

              if (response.success) {
                profilePostCount[profileId] += 1;
                groupSuccessStatus[groupId] = true;

                const updatedUser: User = { campaign_id: Number(campaignId), profileId, numbersend: profilePostCount[profileId]}
                const isUpdatedUser = this.campaignsServices.update_listUser(updatedUser);
                if (!isUpdatedUser) {
                  log.error(`Lỗi cập nhật số lượt chạy cho tài khoản ${profileId}`);
                }

                const updatedGroup: Group = { campaign_id: campaignId, groupID: groupId, status: 'done', postId: response.postId || '' };
                const isUpdatedGroup = this.campaignsServices.updateListGroupStatus(updatedGroup);
                if (!isUpdatedGroup) {
                  log.error(`Lỗi cập nhật trạng thái cho nhóm ${groupId} trong chiến dịch ${campaignId}`);
                }

                const userGroup: UserGroup = { groupId: Number(groupId), userpost: session.user.username, profileId, status: 'done' };
                const isUpdatedUserGroup = this.campaignsServices.inserUserGroup(campaignId, userGroup);
                if (!isUpdatedUserGroup) {
                  log.error(`Lỗi cập nhật tài khoản đăng bài vào nhóm ${groupId} trong chiến dịch ${campaignId}`);
                }

                sendProgress({ message: `tài khoản ${session.user.username} đã gửi thành công nhóm ${groupId}`, campaignId });

              } else {
                const userGroup: UserGroup = { groupId: Number(groupId), userpost: session.user.username, profileId, status: 'false' };
                const isUpdatedUserGroup = this.campaignsServices.inserUserGroup(campaignId, userGroup);
                if (!isUpdatedUserGroup) {
                  log.error(`Lỗi cập nhật tài khoản đăng bài vào nhóm ${groupId} trong chiến dịch ${campaignId}`);
                }
                
                sendProgress({message: `tài khoản ${session.user.username} đăng bài vào nhóm ${groupId} thất bại`, campaignId});
              }
            } catch (err) {
              console.warn(`❌ Lỗi khi ${profileId} gửi nhóm ${groupId}`, err);
            } finally{
              groupLocked.delete(groupId);
            }
          })();

          tasks.push(task);
        }

        await Promise.all(tasks);

        const stillAvailable = profileIds.filter(pid => profilePostCount[pid] < max_post);
        const stillGroups = groupIds.filter(
          gid => !groupSuccessStatus[gid] && stillAvailable.some(pid => !groupTriedBy[gid].has(pid))
        );

        if (stillGroups.length > 0 && stillAvailable.length > 0) {
          await delay(campaignDelay * 1000);
        } else {
          break;
        }
      }

      // tat ca nhom chua gui bai
      for (const groupId of groupIds) {
        if (!groupSuccessStatus[groupId]) {
          const updatedGroup: Group = { campaign_id: campaignId, groupID: groupId, status: 'false', postId: '' };
          const isUpdatedGroup = this.campaignsServices.updateListGroupStatus(updatedGroup);
          if (!isUpdatedGroup) {
            log.error(`Lỗi cập nhật trạng thái cho nhóm ${groupId} trong chiến dịch ${campaignId}`);
          }
        }
      }

      // Đóng tất cả page
      await Promise.all(Object.values(profileSessions).map(s => s.page.close()));
      const isUpdatedCamp = this.campaignsServices.updateCampaignStatus(campaignId, "done");
      if (!isUpdatedCamp) {
        log.error(`Lỗi cập nhật trạng thái Done cho chiến dịch ${campaignId}`);
      }
      const updatedUsers = finalUserList.map((User) => {
        const isUpdated = this.accountFBServices.updateUser({ ...User, status : 'active'});
        if (isUpdated) {
          return User.username;
        }
      });

      if (updatedUsers.length === finalUserList.length) {
        sendProgress({ message: 'Chiến dịch đã hoàn thành', campaignId });
        return { success: true, message: 'Chiến dịch đã hoàn thành' };
      }

      sendProgress({ message: 'Lỗi cập nhật trạng thái cho một số tài khoản', campaignId });
      return { success: false, message: 'Lỗi cập nhật trạng thái cho một số tài khoản' };
    } catch (error) {
      return { success: false, message: error?.toString() || 'Lỗi không xác định' };
    }
  }

  stopbycampaign(CampaignId : string) {
    this.stoppedCampaigns[CampaignId] = true;
    return { success: true, message: 'Chiến dịch đã dừng thành công' };
  }

  stopAll() {
    this.stoppall = true;

    const allCampaigns = this.campaignsServices.getAllCampaigns();
    const runningCampaigns = allCampaigns.filter(c => c.status === 'running');
    if (runningCampaigns.length === 0) {
      return { success: true, message: 'Không có chiến dịch nào đang chạy' };
    }

    const result = runningCampaigns.map((campaign) => {
      this.stoppedCampaigns[campaign.id] = true;
      const isUpdated = this.campaignsServices.updateCampaignStatus(campaign.id, 'stopped');
      if (isUpdated) {
        return campaign.id;
      }
    })
    if (result.length === runningCampaigns.length) {
      this.stoppall = false;
      return { success: true, message: 'Tất cả chiến dịch đã dừng thành công' };
    }

    log.error(`Lỗi khi dừng một số chiến dịch: ${result.join(', ')}`);
    return { success: false, message: 'Lỗi khi dừng một số chiến dịch' };
  }
}
