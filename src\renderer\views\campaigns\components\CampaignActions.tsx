import React from 'react';
import { CircularProgress } from '@mui/material';
import StopIcon from '@mui/icons-material/Stop';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Action } from '../../../components/table/TableConfig';
import { Campaign } from '../../../../interfaces/Campaigns';

interface CampaignActionsProps {
  campaign: Campaign;
  isRunning: boolean;
  isDone: boolean;
  isStopping: boolean;
  onRun: (campaign: Campaign) => void;
  onStop: (campaign: Campaign) => void;
  onEdit: (campaign: Campaign) => void;
  onDelete: (campaign: Campaign) => void;
}

/**
 * Get campaign-specific action button configuration
 * @param props - Campaign actions props
 * @returns Action configuration for run/stop button
 */
export const getCampaignRunStopAction = ({
  campaign,
  isRunning,
  isDone,
  isStopping,
  onRun,
  onStop,
}: Pick<CampaignActionsProps, 'campaign' | 'isRunning' | 'isDone' | 'isStopping' | 'onRun' | 'onStop'>): Action<Campaign> => {
  if (isDone) {
    return {
      key: 'done',
      label: 'Đã xong',
      icon: <StopIcon />,
      onClick: () => {},
      disabled: () => true,
      color: 'inherit',
    };
  }

  if (isStopping && isRunning) {
    return {
      key: 'stopping',
      label: 'Đang dừng...',
      icon: <CircularProgress size={20} />,
      onClick: () => {},
      disabled: () => true,
      color: 'inherit',
    };
  }

  return {
    key: 'runstop',
    label: isRunning ? 'Dừng' : 'Chạy',
    icon: isRunning ? <StopIcon /> : <PlayArrowIcon />,
    onClick: () => (isRunning ? onStop(campaign) : onRun(campaign)),
    color: isRunning ? 'error' : 'primary',
  };
};

/**
 * Get standard campaign action buttons (edit, delete)
 * @param props - Campaign actions props
 * @returns Array of standard action configurations
 */
export const getStandardCampaignActions = ({
  onEdit,
  onDelete,
}: Pick<CampaignActionsProps, 'onEdit' | 'onDelete'>): Action<Campaign>[] => [
  {
    key: 'edit',
    label: 'Chỉnh sửa',
    icon: <EditIcon />,
    hidden: (item: Campaign) =>
      item.status === 'running' || item.status === 'done',
    onClick: (item: Campaign) => onEdit(item),
  },
  {
    key: 'delete',
    label: 'Xóa',
    icon: <DeleteIcon />,
    color: 'error',
    hidden: (item: Campaign) => item.status === 'running',
    onClick: (item: Campaign) => onDelete(item),
  },
];
