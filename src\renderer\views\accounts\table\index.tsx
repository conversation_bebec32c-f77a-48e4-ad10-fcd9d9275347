/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useState } from 'react';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import { Box, useTheme, alpha } from '@mui/material';
import { emptyRows, useTable } from '../../../components/table/TableConfig';
import {
  TableHeadCustom,
  TableEmptyRows,
  EmptyTable,
  TableRowCustom,
} from '../../../components/table';

import { useAccountContext } from '../context';
import AccountForm from '../modal';
import ConfirmModal from '../../../components/modals/ConfirmModal';

// Refactored components
import AccountTableHeader from '../components/AccountTableHeader';
import SearchAndFilter from '../../../components/SearchAndFilter';
import TableInfoHeader from '../../../components/TableInfoHeader';
import AccountTableSkeleton from '../components/AccountTableSkeleton';
import { getAccountTableColumns } from '../components/AccountTableColumns';
import { getAccountActions } from '../components/AccountActions';

// Custom hooks
import { useAccountActions } from '../hooks/useAccountActions';
import { useAccountSearch } from '../hooks/useAccountSearch';

export default function AccountList() {
  const theme = useTheme();
  const { loading, accounts } = useAccountContext();

  // Custom hooks for business logic
  const { searchQuery, setSearchQuery, filteredAccounts } = useAccountSearch();
  const {
    selectedAccount,
    passwordVisibility,
    open,
    togglePasswordVisibility,
    handleClose,
    onDelete,
    handleAccount,
    openBrowser,
    handleBulkDelete,
  } = useAccountActions();

  // Enhanced UI state for confirmation dialog
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({
    open: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });
  // Table state management
  const {
    page,
    order,
    orderBy,
    rowsPerPage,
    selected,
    sortedData,
    totalRows,
    onSort,
    onSelectAllRows,
    onSelectRow,
    onChangePage,
    onChangeRowsPerPage,
  } = useTable({ data: filteredAccounts, total: filteredAccounts.length });

  const handleConfirmDialog = useCallback(
    (title: string, message: string, onConfirm: () => void) => {
      setConfirmDialog({
        open: true,
        title,
        message,
        onConfirm,
      });
    },
    [],
  );

  const handleCloseConfirmDialog = useCallback(() => {
    setConfirmDialog((prev) => ({ ...prev, open: false }));
  }, []);

  // Bulk selection handler
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allIds = filteredAccounts.map((account) => account.id);
        onSelectAllRows(true, allIds);
      } else {
        onSelectAllRows(false, []);
      }
    },
    [filteredAccounts, onSelectAllRows],
  );

  // Bulk delete handler using the refactored hook
  const handleBulkDeleteClick = useCallback(() => {
    if (selected.length === 0) return;

    handleConfirmDialog(
      'Xóa tài khoản',
      `Bạn có chắc chắn muốn xóa ${selected.length} tài khoản đã chọn?`,
      async () => {
        await handleBulkDelete(selected.map((id) => id.toString()));
        onSelectAllRows(false, []);
      },
    );
  }, [selected, handleConfirmDialog, handleBulkDelete, onSelectAllRows]);

  // Table configuration using refactored components
  const accountColumns = getAccountTableColumns({
    passwordVisibility,
    onTogglePasswordVisibility: togglePasswordVisibility,
  });

  // Action handlers and configurations are now handled by custom hooks
  const accountActions = getAccountActions({
    onOpenBrowser: openBrowser,
    onEdit: handleAccount,
    onDelete,
  });

  if (loading) {
    return <AccountTableSkeleton theme={theme} />;
  }

  return (
    <Box sx={{ py: 3 }}>
      {/* Account Table Header */}
      <AccountTableHeader
        selectedCount={selected.length}
        onAdd={() => handleAccount()}
        onBulkDelete={handleBulkDeleteClick}
      />

      {/* Search and Filter Section */}
      <SearchAndFilter
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        placeholder="Tìm kiếm tài khoản..."
      />

      {/* Modern Table Card */}
      <Card
        elevation={2}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        {/* Enhanced Table Header */}
        <TableInfoHeader
          title="Danh sách tài khoản"
          totalCount={filteredAccounts.length}
          selectedCount={selected.length}
          onRefresh={() => window.location.reload()}
        />

        <TableContainer
          sx={{
            // Custom compact horizontal scrollbar
            '&::-webkit-scrollbar': {
              height: 8,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: alpha(theme.palette.grey[300] || '#e0e0e0', 0.5),
              borderRadius: 4,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.8),
              borderRadius: 4,
              '&:hover': {
                backgroundColor: theme.palette.grey[600] || '#757575',
              },
            },
            '&::-webkit-scrollbar-corner': {
              backgroundColor: 'transparent',
            },
            // Firefox scrollbar styling
            scrollbarWidth: 'thin',
            scrollbarColor: `${alpha(theme.palette.grey[500] || '#9e9e9e', 0.8)} ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
          }}
        >
          <Table
            sx={{
              width: '100%',
              '& .MuiTableHead-root': {
                backgroundColor: alpha(
                  theme.palette.grey[50] || '#fafafa',
                  0.8,
                ),
              },
              '& .MuiTableHead-root .MuiTableCell-root': {
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.5px',
                color: theme.palette.text.secondary,
                borderBottom: `2px solid ${theme.palette.divider}`,
              },
              '& .MuiTableBody-root .MuiTableRow-root': {
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: alpha(
                    theme.palette.primary.main || '#1976d2',
                    0.04,
                  ),
                  transform: 'translateY(-1px)',
                  boxShadow: `0 4px 8px ${alpha(theme.palette.common.black || '#000000', 0.1)}`,
                },
                '&.Mui-selected': {
                  backgroundColor: alpha(
                    theme.palette.primary.main || '#1976d2',
                    0.08,
                  ),
                  '&:hover': {
                    backgroundColor: alpha(
                      theme.palette.primary.main || '#1976d2',
                      0.12,
                    ),
                  },
                },
              },
            }}
          >
            <TableHeadCustom
              columns={accountColumns}
              order={order}
              orderBy={orderBy}
              rowCount={totalRows}
              numSelected={selected.length}
              onSort={onSort}
              onSelectAllRows={(checked) =>
                onSelectAllRows(
                  checked,
                  sortedData.map((item) => item.id),
                )
              }
            />
            <TableBody>
              {sortedData.map((item) => (
                <TableRowCustom
                  key={item.id}
                  item={item}
                  columns={accountColumns}
                  actions={accountActions}
                  onSelect={onSelectRow}
                />
              ))}

              <TableEmptyRows
                height={68}
                emptyRows={emptyRows(page, rowsPerPage, accounts.length)}
              />

              {totalRows === 0 && <EmptyTable />}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          page={page}
          count={totalRows}
          rowsPerPage={rowsPerPage}
          onPageChange={onChangePage}
          rowsPerPageOptions={[10, 25, 50]}
          onRowsPerPageChange={onChangeRowsPerPage}
          labelRowsPerPage="Số hàng mỗi trang:"
          labelDisplayedRows={({ from, to, count }) =>
            `Đang hiển thị ${from} đến ${to} của ${count} hàng`
          }
        />
      </Card>

      <AccountForm
        open={open}
        onClose={handleClose}
        account={selectedAccount}
      />

      {/* Confirmation Dialog */}
      <ConfirmModal
        open={confirmDialog.open}
        onClose={handleCloseConfirmDialog}
        onSubmit={confirmDialog.onConfirm}
        title={confirmDialog.title}
        content={confirmDialog.message}
        actions={{ cancel: 'Hủy', submit: 'Xác nhận' }}
      />
    </Box>
  );
}
