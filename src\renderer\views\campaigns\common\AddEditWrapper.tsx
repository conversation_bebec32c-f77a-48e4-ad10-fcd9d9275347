import React, { useEffect, Suspense, lazy } from 'react';
import { useParams } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { CampaignType } from '../CampaignConfig';

// Dynamic imports for better code splitting
const FormPostStepper = lazy(
  () =>
    import('../post/index.tsx') as unknown as Promise<{
      default: React.ComponentType<any>;
    }>,
);
const FormCommentStepper = lazy(
  () =>
    import('../comment/index.tsx') as unknown as Promise<{
      default: React.ComponentType<any>;
    }>,
);
const FormProfileStepper = lazy(
  () =>
    import('../profile/index.tsx') as unknown as Promise<{
      default: React.ComponentType<any>;
    }>,
);

// Loading component for better UX
const LoadingFallback = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '400px',
      py: 4,
    }}
  >
    <CircularProgress size={40} />
  </Box>
);

export default function AddEditWrapper() {
  const { type } = useParams<{ type: CampaignType; id?: string }>();

  const renderForm = () => {
    switch (type) {
      case 'post':
        return <FormPostStepper />;
      case 'comment':
        return <FormCommentStepper />;
      case 'profile':
        return <FormProfileStepper />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ py: 4, px: 0 }}>
      <Suspense fallback={<LoadingFallback />}>{renderForm()}</Suspense>
    </Box>
  );
}
