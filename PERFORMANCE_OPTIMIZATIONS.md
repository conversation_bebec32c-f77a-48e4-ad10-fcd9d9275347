# Campaign AddEditWrapper Performance Optimizations

## Problem Analysis

The AddEditWrapper component was experiencing slow initial loading times (several seconds) on first app load, while subsequent visits were much faster. This indicated a cache loading issue rather than a fundamental performance problem.

## Root Causes Identified

1. **Artificial Delays in Context Providers**: The CampaignProvider had intentional 500ms delays using `setTimeout`
2. **Synchronous Component Loading**: All stepper components were loaded synchronously even when only one was needed
3. **Inefficient Bundle Splitting**: Webpack configuration lacked optimization for code splitting
4. **Heavy Dependencies**: Components imported many heavy libraries (Framer Motion, Material-UI, etc.) upfront

## Optimizations Implemented

### 1. Removed Artificial Delays from CampaignProvider
**File**: `src/renderer/views/campaigns/context.tsx`

**Before**:
```typescript
useEffect(() => {
  setLoading(true);
  setTimeout(async () => {
    // ... fetch logic
  }, 500); // ❌ Artificial delay
}, []);
```

**After**:
```typescript
useEffect(() => {
  const loadCampaigns = async () => {
    setLoading(true);
    try {
      const response = await fetchCampaigns();
      setCampaigns(response);
    } catch (error) {
      console.error('CampaignContext: Error fetching campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  loadCampaigns();
}, []);
```

**Impact**: Eliminates 500ms artificial delay on every context initialization.

### 2. Implemented Dynamic Imports for Stepper Components
**File**: `src/renderer/views/campaigns/common/AddEditWrapper.tsx`

**Before**:
```typescript
import FormPostStepper from '../post';
import FormCommentStepper from '../comment';
import FormProfileStepper from '../profile';
```

**After**:
```typescript
const FormPostStepper = lazy(() => import('../post/index.tsx'));
const FormCommentStepper = lazy(() => import('../comment/index.tsx'));
const FormProfileStepper = lazy(() => import('../profile/index.tsx'));

// With Suspense wrapper
<Suspense fallback={<LoadingFallback />}>
  {renderForm()}
</Suspense>
```

**Impact**: Only loads the required stepper component based on campaign type, reducing initial bundle size.

### 3. Added Intelligent Preloading Strategy
**File**: `src/renderer/utils/preloader.ts` (New)

Created a comprehensive preloading system that:
- Preloads components in the background when users navigate to campaigns section
- Provides intelligent caching to avoid duplicate loads
- Supports both bulk preloading and specific component preloading

**Usage**:
```typescript
// Preload all campaign components
preloadCampaignComponents();

// Preload specific component based on type
preloadCampaignComponent('post');
```

**Impact**: Improves perceived performance by loading components before they're needed.

### 4. Enhanced Webpack Bundle Splitting
**Files**: 
- `.erb/configs/webpack.config.renderer.prod.ts`
- `.erb/configs/webpack.config.renderer.dev.ts`

**Added optimizations**:
```typescript
splitChunks: {
  chunks: 'all',
  cacheGroups: {
    vendor: { /* Node modules */ },
    mui: { /* Material-UI specific */ },
    framerMotion: { /* Framer Motion specific */ },
    campaigns: { /* Campaign components */ },
    components: { /* Common components */ },
  },
},
concatenateModules: true,
sideEffects: false,
```

**Impact**: Better code splitting, improved caching, and smaller initial bundles.

### 5. Removed Additional Artificial Delays
**File**: `src/renderer/hooks/useCampaignRun.tsx`

Removed 200ms artificial delay in user fetching logic.

## Performance Improvements Expected

1. **Initial Load Time**: Reduced from several seconds to under 1 second
2. **Bundle Size**: Smaller initial bundles due to code splitting
3. **Caching**: Better browser caching with optimized chunk splitting
4. **Perceived Performance**: Immediate loading indicators with Suspense
5. **Background Loading**: Components preload while users navigate

## Testing Recommendations

1. **Clear Browser Cache**: Test with fresh cache to simulate first-time users
2. **Network Throttling**: Test with slow 3G to verify loading improvements
3. **Bundle Analysis**: Use `npm run build` with `ANALYZE=true` to verify chunk sizes
4. **Performance Monitoring**: Add performance marks to measure actual load times

## Monitoring

Add these console logs to monitor performance:
```typescript
console.time('AddEditWrapper-Load');
// ... component logic
console.timeEnd('AddEditWrapper-Load');
```

## Future Optimizations

1. **Service Worker**: Implement service worker for aggressive caching
2. **Resource Hints**: Add `<link rel="preload">` for critical resources
3. **Tree Shaking**: Further optimize Material-UI imports
4. **Virtual Scrolling**: For large lists in stepper components
5. **Image Optimization**: Optimize any images used in campaign forms

## Rollback Plan

If issues arise, revert changes in this order:
1. Webpack configuration changes
2. Preloading system
3. Dynamic imports (revert to static imports)
4. Context provider optimizations (add back delays if needed)
