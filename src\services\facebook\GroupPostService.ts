import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer-core';
import log from '../../utils/logs';
import { delay } from '../../utils/helper';
import { FBContentPostRequest } from '../../types/FacebookGroup';
import { ResultCommon } from '../../types/Result';

export default class GroupPostService {
  protected fbGroupUrl: string = 'https://www.facebook.com/groups';

  protected textBoxSelector: string = 'div[contenteditable="true"][role="textbox"]';

  protected btnSelector: string = 'div[role="button"][tabindex="0"]';

  protected formPostSelector: string = 'div[role="dialog"] form';

  /**
   * Search groups on Facebook by value
   * @param profileId
   * @param value
   * @returns GroupSearchResult
   */
  async postToGroup(page: Page, groupId: string, user: any, {message, filePaths = []}: FBContentPostRequest, isJoinGroup: boolean): Promise<ResultCommon> {

    await page.evaluate((url) => {
      window.location.href = url;
    }, `${this.fbGroupUrl}/${groupId}`);
    await page.waitForNavigation({ waitUntil: 'domcontentloaded' });

    await delay(1500);

    await page.evaluate(() => {
      window.scrollTo(0, 250);
    });

    try {

      // find post box
      const postBox = await this.findPostBox(page, groupId, user);
      if (!postBox) {
        return { success: false, error: 'Không được phép đăng bài trong nhóm' }
      }

      await postBox.click();

      // modal post box
      const selectors = await this.findTextBox(page);
      if (!selectors || !selectors.textBox) {
        return { success: false, error: 'Không được phép đăng bài trong nhóm' }
      }

      const { form, textBox } = selectors;

      await delay(1000);
      await textBox.click();

      // paste message
      if (message) {
        await page.evaluate((contentDiv, textContent) => {

        const pasteEvent = new ClipboardEvent('paste', {
          bubbles: true,
          cancelable: true,
          clipboardData: new DataTransfer(),
        });

        pasteEvent.clipboardData!.setData('text/plain', textContent);
        contentDiv.dispatchEvent(pasteEvent);

        }, textBox, message);
      }

      // handle upload file
      if (filePaths.length > 0) {
        const count = await GroupPostService.postFile(page, filePaths);
        log.info('Number images is uploaded: ', count);
      }

      // submit form
      await page.evaluate((formElement: HTMLFormElement) => {
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        formElement.dispatchEvent(submitEvent);
      }, form as ElementHandle<HTMLFormElement>);

      if (isJoinGroup) {
        console.log('✅ Đã gửi yêu cầu tham gia nhóm');
      }

      await delay(5000); // Min = 5s tránh lỗi submit form
      const postId = await GroupPostService.getPostIdAfterSubmit(page, groupId, user);
      return { success: true, postId: postId || '' };

    } catch (error: any) {
      log.error('Post to group error:', error);
      return { success: false, error: error.message, postId: '' };
    }
  }

private static async getPostIdAfterSubmit(page: Page, groupId: string, user: any) {
  await page.goto(`https://www.facebook.com/${user.userId}/allactivity`);

  const postLink = await page.$(
    `a[href*="/groups/${groupId}/"][href*="/permalink/"], a[href*="/groups/${groupId}/"][href*="/pending_posts/"]`
  );

  if (postLink) {
    const href = await postLink.evaluate(el => el.getAttribute('href'));
    const match = href?.match(new RegExp(`/groups/${groupId}/(?:permalink|pending_posts)/(\\d+)`));

    if (match) {
      const postId = match[1];
      console.log('✅ groupId:', groupId, 'postId:', postId);
      return postId;
    }
  } else {
    console.log('❌ Không tìm thấy link bài viết');
  }

  return null;
}


  /**
   * Find post box
   *
   * PostBox will contains user's href if user can post content. Href such as:
   *
   * "https://www.facebook.com/groups/${groupId}/user/${userId}"
   *
   * "https://www.facebook.com/${username}"
   *
   * "https://www.facebook.com/profile.php?id=${userId}"
   *
   * @param page
   * @returns postBox | null
   */
  private async findPostBox(page: Page, groupId: string, user: any) {

    await delay(1000);
    const container = await page.evaluateHandle((gId: string, u: any) => {
      const hrefGroup = `https://www.facebook.com/groups/${gId}/user/${u.userId}/`;
      const hrefProfile = `https://www.facebook.com/profile.php?id=${u.userId}`;

      const groups: HTMLAnchorElement[] = [];

      const anchors = document.querySelectorAll('a');
      anchors.forEach((link) => {
        if (link.href === hrefProfile || link.href === hrefGroup) {
          groups.push(link);
        }
        if (u.username) {
          const hrefUsername = `https://www.facebook.com/${u.username}`;
          if (link.href === hrefUsername) {
            groups.push(link);
          }
        }
      });

      if (groups.length === 0) {
        return null;
      }

      const anchor = groups[groups.length -1];
      return anchor.closest('div');

    }, groupId, user);

    if (container.asElement()) {
      const boxContainer = container as ElementHandle;
      const postBox = await boxContainer.$(this.btnSelector);
      return postBox;
    }

    return null;
  }

    /**
   * Find text box
   * @param page
   */
  private async findTextBox(page: Page) {
    let form = null;
    try {
      form = await page.waitForSelector(this.formPostSelector, { timeout: 3000 });
    } catch (error) {
      log.info('timeout wait for text box', error);
    }

    if (form === null) {
      return null;
    }

    const textBox = await form.$(this.textBoxSelector);
    if (!textBox) {
      return null;
    }

    return { form, textBox }
  }

  /**
   * Handle post file
   * @param page
   * @param fileBase64
   * @param fileName
   * @returns number images uploaded
   */
  protected static async postFile(page: Page, filePaths: Array<string>): Promise<number> {
    try {
      const fileInput = await page.$('div[role="dialog"] form input[type="file"][accept*="image/*"]');
      log.info("query file input", fileInput);
      if (!fileInput) {
        log.error('Input upload file not found');
        return 0;
      }

      await fileInput.uploadFile(...filePaths);

      // wait until number images uploaded = file paths length
      await page.waitForFunction(
        (expectedCount) => {
          const images = document.querySelectorAll('div[role="dialog"] form div[role="group"] img');
          return images.length === expectedCount;
        },
        { timeout: 10000 },
        filePaths.length
      );

      const imageCount = await page.evaluate(() => {
        return document.querySelectorAll('div[role="dialog"] form div[role="group"] img').length;
      });

      // dispatch input change for prepare event submit form
      await page.evaluate((input) => {
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);
      }, fileInput);

      return imageCount;

    } catch (error: any) {
      log.error('Upload image error:', error);
      return 0;
    }
  }

}
