/* eslint-disable react/no-array-index-key */
import { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Box,
  Divider,
  Chip,
  Paper,
  Avatar,
  IconButton,
  Alert,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  TableContainer,
  Tooltip,
  Stack,
  useTheme,
  alpha,
  Button,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Schedule as ScheduleIcon,
  Settings as SettingsIcon,
  Group as GroupIcon,
  Image as ImageIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
} from '@mui/icons-material';

import { CampaignDetails } from '../../../../interfaces/Campaigns';
import CampaignDetailSkeleton from '../../../components/skeletons/CampaignDetailSkeleton';
import { formatDate } from '../../../../utils/format';

// Thêm type cho ProgressLog để đảm bảo an toàn kiểu dữ liệu
interface ProgressLog {
  message: string;
  campaignId: string;
}

const getStatusColor = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'running':
      return 'success';
    case 'stopped':
    case 'paused':
      return 'warning';
    case 'done':
    case 'completed':
    case 'true':
      return 'info';
    case 'new':
      return 'default';
    case 'error':
    case 'failed':
    case 'false':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusIcon = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'running':
      return <PlayArrowIcon />;
    case 'stopped':
    case 'paused':
      return <StopIcon />;
    case 'done':
    case 'completed':
    case 'true':
      return <CheckCircleIcon />;
    case 'error':
    case 'failed':
    case 'false':
      return <CancelIcon />;
    default:
      return <InfoIcon />;
  }
};

const getStatusLabel = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'running':
      return 'Đang chạy';
    case 'stopped':
    case 'paused':
      return 'Đã dừng';
    case 'done':
    case 'completed':
    case 'true':
      return 'Hoàn thành';
    case 'new':
      return 'Mới tạo';
    case 'error':
    case 'failed':
    case 'false':
      return 'Lỗi';
    default:
      return 'Không xác định';
  }
};

export default function CampaignDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [campaignDetail, setCampaignDetail] = useState<CampaignDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sửa lỗi: Thay đổi kiểu dữ liệu của logs để lưu trữ message và status
  const [logs, setLogs] = useState<ProgressLog[]>([]);
  const logEndRef = useRef<HTMLDivElement | null>(null);

  // Sửa lỗi: Cập nhật state logs và cuộn xuống cuối mỗi khi có log mới
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  // Sửa lỗi: Hàm addLog giờ đây nhận một đối tượng ProgressLog
  const addLog = (newLog: ProgressLog) => {
    setLogs((prev) => [...prev, newLog]);
  };

  // Sửa lỗi: Cập nhật useEffect để lắng nghe sự kiện và cập nhật logs
  useEffect(() => {
    if (!id) return undefined;

    const cleanup = window.facebookWeb.onProgress((data) => {
      // Thêm log mới vào state
      addLog({
        message: data.message,
        campaignId: data.campaignId,
      });

      if (data.action === 'stopped') {
        // set stopped for campaign
      }

      console.log('mesage', data.message);
    });

    return cleanup;
  }, [id]);

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await window.Campaigns.getCampaignDetails(id);
        setCampaignDetail(data);
      } catch (err) {
        console.error('Error fetching campaign details:', err);
        setError('Không thể tải thông tin chiến dịch. Vui lòng thử lại.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Loading state
  if (loading) {
    return <CampaignDetailSkeleton />;
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={1}
          sx={{
            p: 4,
            borderRadius: 3,
            textAlign: 'center',
            border: `1px solid ${alpha(theme.palette.error.main || '#f44336', 0.2)}`,
          }}
        >
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 2,
              '& .MuiAlert-message': {
                fontSize: '1rem',
              },
            }}
          >
            {error}
          </Alert>
          <Button
            variant="contained"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/campaign/all')}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
            }}
          >
            Quay lại danh sách
          </Button>
        </Paper>
      </Box>
    );
  }

  // No data state
  if (!campaignDetail) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={1}
          sx={{
            p: 4,
            borderRadius: 3,
            textAlign: 'center',
            border: `1px solid ${alpha(theme.palette.warning.main || '#ff9800', 0.2)}`,
          }}
        >
          <Alert
            severity="warning"
            sx={{
              mb: 3,
              borderRadius: 2,
              '& .MuiAlert-message': {
                fontSize: '1rem',
              },
            }}
          >
            Không tìm thấy thông tin chiến dịch.
          </Alert>
          <Button
            variant="contained"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/campaign/all')}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
            }}
          >
            Quay lại danh sách
          </Button>
        </Paper>
      </Box>
    );
  }

  const { Campaign, CampaignConfiguration, Group } = campaignDetail;

  const Image = [
    {
      image: 'E:\VIE\Ảnh\cropped-logoTOP.png',
      id: 1,
    },
  ];

  return (
    <Box>
      {/* Modern Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main || '#1976d2'} 0%, ${theme.palette.primary.dark || '#1565c0'} 100%)`,
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <IconButton
            onClick={() => navigate(-1)}
            sx={{
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
              },
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
              Chiến dịch: {Campaign?.name || 'Đang tải thông tin...'}
            </Typography>
          </Box>
          {Campaign?.status && (
            <Chip
              icon={getStatusIcon(Campaign.status)}
              label={getStatusLabel(Campaign.status)}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontWeight: 600,
                '& .MuiChip-icon': {
                  color: 'white',
                },
              }}
            />
          )}
        </Stack>
      </Paper>

      <Grid container spacing={3}>
        {/* Left Column - Campaign Information */}
        <Grid size={{ xs: 12, lg: 8 }}>
          {/* Campaign Information Section */}
          <Card
            elevation={1}
            sx={{
              borderRadius: 3,
              border: `1px solid ${theme.palette.divider}`,
              overflow: 'hidden',
              mb: 3,
            }}
          >
            <Box
              sx={{
                p: 3,
                backgroundColor: alpha(
                  theme.palette.primary.main || '#1976d2',
                  0.04,
                ),
                borderBottom: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Stack direction="row" spacing={2} alignItems="center">
                <Avatar
                  sx={{
                    width: 56,
                    height: 56,
                  }}
                >
                  {getStatusIcon(Campaign?.status)}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {Campaign?.name || 'Không có tên'}
                  </Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Typography variant="body2" color="text.secondary">
                      Loại:{' '}
                      <strong>{Campaign?.type || 'Không xác định'}</strong>
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • ID: <strong>{Campaign?.id || 'N/A'}</strong>
                    </Typography>
                  </Stack>
                </Box>
              </Stack>
            </Box>

            <CardContent sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Paper
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(
                        theme.palette.grey[50] || '#fafafa',
                        0.8,
                      ),
                      border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={1}
                      sx={{ mb: 1 }}
                    >
                      <ScheduleIcon
                        sx={{ color: 'text.secondary', fontSize: 20 }}
                      />
                      <Typography variant="subtitle2" color="text.secondary">
                        Ngày tạo
                      </Typography>
                    </Stack>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {formatDate(Campaign?.created_at || '')}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Paper
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(
                        theme.palette.grey[50] || '#fafafa',
                        0.8,
                      ),
                      border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
                    }}
                  >
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={1}
                      sx={{ mb: 1 }}
                    >
                      <ScheduleIcon
                        sx={{ color: 'text.secondary', fontSize: 20 }}
                      />
                      <Typography variant="subtitle2" color="text.secondary">
                        Cập nhật lần cuối
                      </Typography>
                    </Stack>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {Campaign?.updated_at
                        ? formatDate(Campaign.updated_at, 'datetime')
                        : '---'}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Campaign Configuration Section */}
          <Grid size={{ xs: 12, lg: 12 }} sx={{ mb: 3 }}>
            <Card
              elevation={1}
              sx={{
                borderRadius: 3,
                border: `1px solid ${theme.palette.divider}`,
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  p: 3,
                  backgroundColor: alpha(
                    theme.palette.secondary.main || '#9c27b0',
                    0.04,
                  ),
                  borderBottom: `1px solid ${theme.palette.divider}`,
                }}
              >
                <Stack direction="row" spacing={2} alignItems="center">
                  <Avatar
                    sx={{
                      width: 56,
                      height: 56,
                      backgroundColor:
                        theme.palette.secondary.main || '#9c27b0',
                    }}
                  >
                    <SettingsIcon />
                  </Avatar>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Cấu hình chiến dịch
                  </Typography>
                </Stack>
              </Box>

              <CardContent sx={{ p: 3 }}>
                {CampaignConfiguration ? (
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Paper
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          borderRadius: 2,
                          backgroundColor: alpha(
                            theme.palette.primary.main || '#1976d2',
                            0.04,
                          ),
                          border: `1px solid ${alpha(theme.palette.primary.main || '#1976d2', 0.2)}`,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[4],
                          },
                        }}
                      >
                        <Typography
                          variant="h4"
                          color="primary"
                          sx={{ fontWeight: 600, mb: 1 }}
                        >
                          {CampaignConfiguration.delay ?? 5}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Delay tối đa (giây)
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Paper
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          borderRadius: 2,
                          backgroundColor: alpha(
                            theme.palette.success.main || '#4caf50',
                            0.04,
                          ),
                          border: `1px solid ${alpha(theme.palette.success.main || '#4caf50', 0.2)}`,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[4],
                          },
                        }}
                      >
                        <Typography
                          variant="h4"
                          color="success.main"
                          sx={{ fontWeight: 600, mb: 1 }}
                        >
                          {CampaignConfiguration.max_post ?? 1}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Số bài tối đa
                        </Typography>
                      </Paper>
                    </Grid>

                    {/* Additional Configuration Options */}
                    <Grid size={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Tùy chọn khác
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <List dense>
                            <ListItem>
                              <ListItemAvatar>
                                <Avatar
                                  sx={{
                                    bgcolor: CampaignConfiguration.is_anonymous
                                      ? 'success.main'
                                      : 'grey.300',
                                  }}
                                >
                                  {CampaignConfiguration.is_anonymous ? (
                                    <CheckCircleIcon />
                                  ) : (
                                    <CancelIcon />
                                  )}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary="Ẩn danh"
                                secondary={
                                  CampaignConfiguration.is_anonymous
                                    ? 'Đã bật'
                                    : 'Đã tắt'
                                }
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemAvatar>
                                <Avatar
                                  sx={{
                                    bgcolor: CampaignConfiguration.is_joingroup
                                      ? 'success.main'
                                      : 'grey.300',
                                  }}
                                >
                                  {CampaignConfiguration.is_joingroup ? (
                                    <CheckCircleIcon />
                                  ) : (
                                    <CancelIcon />
                                  )}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary="Tham gia nhóm"
                                secondary={
                                  CampaignConfiguration.is_joingroup
                                    ? 'Đã bật'
                                    : 'Đã tắt'
                                }
                              />
                            </ListItem>
                          </List>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <List dense>
                            <ListItem>
                              <ListItemAvatar>
                                <Avatar
                                  sx={{
                                    bgcolor: CampaignConfiguration.tag_friend
                                      ? 'success.main'
                                      : 'grey.300',
                                  }}
                                >
                                  {CampaignConfiguration.tag_friend ? (
                                    <CheckCircleIcon />
                                  ) : (
                                    <CancelIcon />
                                  )}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary="Gắn thẻ bạn bè"
                                secondary={
                                  CampaignConfiguration.tag_friend
                                    ? 'Đã bật'
                                    : 'Đã tắt'
                                }
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemAvatar>
                                <Avatar
                                  sx={{
                                    bgcolor:
                                      CampaignConfiguration.comment_after_post
                                        ? 'success.main'
                                        : 'grey.300',
                                  }}
                                >
                                  {CampaignConfiguration.comment_after_post ? (
                                    <CheckCircleIcon />
                                  ) : (
                                    <CancelIcon />
                                  )}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary="Bình luận sau đăng"
                                secondary={
                                  CampaignConfiguration.comment_after_post
                                    ? 'Đã bật'
                                    : 'Đã tắt'
                                }
                              />
                            </ListItem>
                          </List>
                        </Grid>
                      </Grid>
                    </Grid>

                    {/* Message Content */}
                    {Campaign?.message && (
                      <Grid size={12}>
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Nội dung tin nhắn:
                          </Typography>
                          <Typography variant="body2">
                            {Campaign?.message}
                          </Typography>
                        </Paper>
                      </Grid>
                    )}

                    {/* Comment Content */}
                    {CampaignConfiguration.comment_content && (
                      <Grid size={12}>
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Nội dung bình luận:
                          </Typography>
                          <Typography variant="body2">
                            {CampaignConfiguration.comment_content}
                          </Typography>
                        </Paper>
                      </Grid>
                    )}
                  </Grid>
                ) : (
                  <Alert severity="info">
                    Không có thông tin cấu hình cho chiến dịch này.
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Images Section */}
          {/* <Card elevation={1} sx={{ mb: 3 }}>
            <CardHeader
              avatar={
                <Avatar>
                  <ImageIcon />
                </Avatar>
              }
              title={`Hình ảnh (${Image?.length || 0})`}
            />
            <CardContent sx={{ py: 0 }}>
              {Image && Image.length > 0 ? (
                <Grid container spacing={1}>
                  {Image.map((img, index) => (
                    <Grid
                      size={{ xs: 12, sm: 6, md: 4, lg: 3 }}
                      key={img.id || index}
                    >
                      <Paper
                        sx={{
                          p: 1,
                          textAlign: 'center',
                          '&:hover': {
                            boxShadow: 3,
                            transform: 'scale(1.02)',
                            transition: 'all 0.2s ease-in-out',
                          },
                        }}
                      >
                        <img
                          src={img.image}
                          alt={`Ảnh chiến dịch ${index + 1}`}
                          style={{
                            width: '100%',
                            height: 150,
                            objectFit: 'cover',
                            borderRadius: 8,
                          }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src =
                              '/placeholder-image.png';
                          }}
                        />
                        <Typography
                          variant="caption"
                          display="block"
                          sx={{ mt: 1 }}
                        >
                          Ảnh {index + 1}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info" icon={<ImageIcon />}>
                  Không có hình ảnh nào được thêm vào chiến dịch này.
                </Alert>
              )}
            </CardContent>
          </Card> */}

          {/* Groups Section */}
          <Card elevation={1}>
            <CardHeader
              avatar={
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <GroupIcon />
                </Avatar>
              }
              title={`Danh sách nhóm (${Group?.length || 0})`}
            />
            <CardContent sx={{ py: 0 }}>
              {Group && Group.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <strong>Group ID</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Post ID</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Trạng thái</strong>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Group.map((group, index) => (
                        <TableRow
                          key={group.id || index}
                          sx={{ '&:hover': { bgcolor: 'grey.50' } }}
                        >
                          <TableCell>
                            <Tooltip title={group.groupID}>
                              <Typography
                                variant="body2"
                                noWrap
                                sx={{ maxWidth: 150 }}
                              >
                                {group.groupID}
                              </Typography>
                            </Tooltip>
                          </TableCell>
                          <TableCell>
                            {group.postId ? (
                              <Tooltip title={group.postId}>
                                <Typography
                                  variant="body2"
                                  noWrap
                                  sx={{ maxWidth: 150 }}
                                >
                                  {group.postId}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                Chưa đăng
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={group.status}
                              color={getStatusColor(group.status) as any}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info" icon={<GroupIcon />}>
                  Không có nhóm nào được thêm vào chiến dịch này.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Right Column - Log Information (Prominent Position) */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card
            elevation={2}
            sx={{
              borderRadius: 3,
              border: `2px solid ${alpha(theme.palette.success.main || '#4caf50', 0.2)}`,
              overflow: 'hidden',
              position: 'sticky',
              top: 24,
              maxHeight: 'calc(100vh - 48px)',
            }}
          >
            <Box
              sx={{
                p: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.success.main || '#4caf50', 0.1)} 0%, ${alpha(theme.palette.success.dark || '#388e3c', 0.05)} 100%)`,
                borderBottom: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Stack direction="row" spacing={2} alignItems="center">
                <Avatar
                  sx={{
                    width: 48,
                    height: 48,
                    backgroundColor: theme.palette.success.main || '#4caf50',
                  }}
                >
                  <InfoIcon />
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    Log thông tin
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Theo dõi tiến trình chiến dịch
                  </Typography>
                </Box>
                <Chip
                  label={`${logs.length} logs`}
                  size="small"
                  color="success"
                  variant="outlined"
                />
              </Stack>
            </Box>

            <CardContent
              sx={{ p: 0, height: 'calc(100vh - 280px)', overflow: 'hidden' }}
            >
              <Box
                sx={{
                  height: '100%',
                  overflowY: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 6,
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: alpha(
                      theme.palette.grey[300] || '#e0e0e0',
                      0.3,
                    ),
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: alpha(
                      theme.palette.grey[500] || '#9e9e9e',
                      0.5,
                    ),
                    borderRadius: 3,
                    '&:hover': {
                      backgroundColor: alpha(
                        theme.palette.grey[600] || '#757575',
                        0.7,
                      ),
                    },
                  },
                }}
              >
                {logs.length > 0 ? (
                  <List sx={{ p: 0 }}>
                    {logs.map((log, index) => (
                      <ListItem
                        key={index}
                        sx={{
                          borderBottom:
                            index < logs.length - 1
                              ? `1px solid ${theme.palette.divider}`
                              : 'none',
                          py: 1,
                          px: 2,
                          '&:hover': {
                            backgroundColor: alpha(
                              theme.palette.success.main || '#4caf50',
                              0.04,
                            ),
                          },
                        }}
                      >
                        <ListItemAvatar>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              backgroundColor: alpha(
                                theme.palette.success.main || '#4caf50',
                                0.1,
                              ),
                              color: theme.palette.success.main || '#4caf50',
                              fontSize: '0.875rem',
                            }}
                          >
                            {index + 1}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 500,
                                lineHeight: 1.4,
                                wordBreak: 'break-word',
                              }}
                            >
                              {log.message}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ mt: 0.5, display: 'block' }}
                            >
                              {new Date().toLocaleTimeString('vi-VN')}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                    <span ref={logEndRef} />
                  </List>
                ) : (
                  <Box
                    sx={{
                      p: 4,
                      textAlign: 'center',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <InfoIcon
                      sx={{
                        fontSize: 48,
                        color: alpha(theme.palette.grey[400] || '#bdbdbd', 0.5),
                        mb: 2,
                      }}
                    />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 1 }}
                    >
                      Chưa có log nào
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Logs sẽ xuất hiện khi chiến dịch bắt đầu chạy
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
